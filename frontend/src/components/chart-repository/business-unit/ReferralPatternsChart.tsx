import React from "react"
import {
  <PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>ianG<PERSON>,
  Legend,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  <PERSON><PERSON><PERSON><PERSON>,
} from "recharts"

import { formatAbbreviatedCurrency } from "@/lib/number"
import { CHART_HEIGHT } from "@/components/CardComponents"

interface ReferralPatternsChartProps {
  data: {
    referralPatterns: Array<{
      source: string
      target: string
      value: number
    }>
    businessUnits: Array<{
      id: string
      name: string
      color: string
    }>
  }
}

const ReferralPatternsChart: React.FC<ReferralPatternsChartProps> = ({
  data,
}) => {
  // Aggregate referrals by source
  const referralsBySource = data.referralPatterns.reduce(
    (acc, { source, value }) => {
      if (!acc[source]) {
        acc[source] = { unit: source, totalReferrals: 0 }
      }
      acc[source].totalReferrals += value
      return acc
    },
    {} as Record<string, { unit: string; totalReferrals: number }>
  )

  // Convert to array and sort by total referrals
  const chartData = Object.values(referralsBySource).sort(
    (a, b) => b.totalReferrals - a.totalReferrals
  )

  return (
    <ResponsiveContainer width="99%" height={CHART_HEIGHT}>
      <BarChart data={chartData} layout="vertical">
        <CartesianGrid strokeDasharray="3 3" />
        <XAxis
          type="number"
          tick={{ fontSize: 12 }}
          tickFormatter={(value) => formatAbbreviatedCurrency(value, 0)}
        />
        <YAxis
          type="category"
          dataKey="unit"
          width={120}
          tick={{ fontSize: 12 }}
        />
        <Tooltip
          formatter={(value, name) => [
            `${formatAbbreviatedCurrency(Number(value), 0)} patients`,
            name,
          ]}
        />
        <Legend
          verticalAlign="top"
          wrapperStyle={{ fontSize: 12, paddingBottom: 12 }}
        />
        <Bar dataKey="totalReferrals" name="Referrals Sent" fill="#8884d8" />
      </BarChart>
    </ResponsiveContainer>
  )
}

export default ReferralPatternsChart
