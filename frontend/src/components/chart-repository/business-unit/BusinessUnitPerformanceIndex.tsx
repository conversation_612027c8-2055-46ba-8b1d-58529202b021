import React from "react"
import {
  Bar,
  Bar<PERSON>hart,
  CartesianGrid,
  Cell,
  Legend,
  ReferenceLine,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  YAxis,
} from "recharts"

import { CHART_HEIGHT } from "@/components/CardComponents"

interface BusinessUnitPerformanceIndexProps {
  data: {
    profitability: Array<{
      unit: string
      revenue: number
      expenses: number
      profit: number
      margin: number
    }>
    patientSatisfaction: Array<{
      unit: string
      score: number
      benchmark: number
      responses: number
    }>
    providerProductivity: Array<{
      unit: string
      patientsPerDay: number
      revenuePerPatient: number
      benchmark: number
    }>
    qualityMetrics: Array<{
      unit: string
      preventiveScreening: number
      chronicDiseaseManagement: number
      readmissionRate: number
    }>
    businessUnits: Array<{
      id: string
      name: string
      color: string
    }>
  }
}

const BusinessUnitPerformanceIndex: React.FC<
  BusinessUnitPerformanceIndexProps
> = ({ data }) => {
  // Calculate a composite performance index for each business unit
  // This combines financial, satisfaction, productivity, and quality metrics
  const performanceData = data.businessUnits
    .filter((unit) => unit.id !== "admin") // Exclude admin
    .map((unit) => {
      const unitName = unit.name

      // Get data for this unit
      const profitData = data.profitability.find(
        (item) => item.unit === unitName
      )
      const satisfactionData = data.patientSatisfaction.find(
        (item) => item.unit === unitName
      )
      const productivityData = data.providerProductivity.find(
        (item) => item.unit === unitName
      )
      const qualityData = data.qualityMetrics.find(
        (item) => item.unit === unitName
      )

      // Skip if any data is missing
      if (
        !profitData ||
        !satisfactionData ||
        !productivityData ||
        !qualityData
      ) {
        return null
      }

      // Calculate normalized scores (0-100) for each dimension
      const financialScore = Math.min(100, Math.max(0, profitData.margin * 2.5)) // margin * 2.5 (40% margin = 100 points)

      const satisfactionScore = Math.min(
        100,
        Math.max(0, satisfactionData.score * 20)
      ) // score * 20 (5.0 = 100 points)

      const productivityScore = Math.min(
        100,
        Math.max(
          0,
          (productivityData.patientsPerDay / productivityData.benchmark) * 100
        )
      )

      const qualityScore = Math.min(
        100,
        Math.max(
          0,
          qualityData.preventiveScreening * 0.4 +
            qualityData.chronicDiseaseManagement * 0.4 +
            (10 - qualityData.readmissionRate) * 2 // Lower readmission is better
        )
      )

      // Calculate weighted composite score
      const compositeScore =
        financialScore * 0.3 +
        satisfactionScore * 0.3 +
        productivityScore * 0.2 +
        qualityScore * 0.2

      return {
        unit: unitName,
        compositeScore: Math.round(compositeScore),
        financialScore: Math.round(financialScore),
        satisfactionScore: Math.round(satisfactionScore),
        productivityScore: Math.round(productivityScore),
        qualityScore: Math.round(qualityScore),
        color: unit.color,
      }
    })
    .filter(Boolean) // Remove null entries
    .sort((a, b) => b!.compositeScore - a!.compositeScore) // Sort by composite score

  return (
    <ResponsiveContainer width="99%" height={CHART_HEIGHT}>
      <BarChart
        data={performanceData as any[]}
        margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
        layout="vertical"
      >
        <CartesianGrid strokeDasharray="3 3" />
        <XAxis type="number" domain={[0, 100]} tick={{ fontSize: 12 }} />
        <YAxis
          type="category"
          dataKey="unit"
          width={100}
          tick={{ fontSize: 12 }}
        />
        <Tooltip
          formatter={(value, name) => [`${value} points`, name]}
          labelFormatter={(label) => `Business Unit: ${label}`}
        />
        <Legend
          verticalAlign="top"
          wrapperStyle={{ fontSize: 12, paddingBottom: 12 }}
        />
        <Bar
          dataKey="compositeScore"
          name="Performance Index (0-100)"
          barSize={20}
        >
          {(performanceData as any[]).map((entry, index) => (
            <Cell key={`cell-${index}`} fill={entry.color} />
          ))}
        </Bar>
        <ReferenceLine
          x={70}
          stroke="#000"
          strokeDasharray="3 3"
          label={{ value: "Target", position: "top", fontSize: 12 }}
        />
      </BarChart>
    </ResponsiveContainer>
  )
}

export default BusinessUnitPerformanceIndex
