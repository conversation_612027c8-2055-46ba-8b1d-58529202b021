"use client"

import React from "react"
import {
  Bar,
  CartesianGrid,
  ComposedChart,
  Legend,
  Line,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  YAxis,
} from "recharts"

import { CHART_HEIGHT } from "@/components/CardComponents"
import { cumulativeNetProfitData } from "@/components/chart-repository/data/cumulativeNetProfitData"

const MONTHS = [
  "Jan",
  "Feb",
  "Mar",
  "Apr",
  "May",
  "Jun",
  "Jul",
  "Aug",
  "Sep",
  "Oct",
  "Nov",
  "Dec",
]

const CURRENT_YEAR = new Date().getFullYear()

const yearColors = {
  2023: "#90CAF9",
  2024: "#2196F3",
  2025: "#0D47A1",
}

const formatYAxis = (value: number) => {
  if (value >= 1_000_000) return `$${(value / 1_000_000).toFixed(1)}M`
  if (value >= 1_000) return `$${(value / 1_000).toFixed(0)}K`
  return `$${value}`
}

const CustomTooltip = ({ active, payload, label }: any) => {
  if (active && payload && payload.length) {
    const allData = payload[0]?.payload
    return (
      <div className="rounded-md border border-gray-200 bg-white p-3 shadow-md">
        <p className="text-sm font-bold">{label}</p>
        {allData.revenue !== undefined && (
          <p className="text-sm text-gray-600">
            Revenue:{" "}
            <span className="font-medium text-blue-600">
              ${(allData.revenue / 1_000_000).toFixed(2)}M
            </span>
          </p>
        )}
        {allData.expenses !== undefined && (
          <p className="text-sm text-gray-600">
            Expenses:{" "}
            <span className="font-medium text-red-600">
              ${(allData.expenses / 1_000_000).toFixed(2)}M
            </span>
          </p>
        )}
        {allData.netProfit !== undefined && (
          <p className="text-sm text-gray-600">
            Net Profit:{" "}
            <span className="font-medium text-green-600">
              ${(allData.netProfit / 1_000_000).toFixed(2)}M
            </span>
          </p>
        )}
        {Object.keys(yearColors).map((year) =>
          allData[`cumulative_${year}`] !== undefined ? (
            <p key={year} className="text-sm text-gray-600">
              {year} Cumulative:{" "}
              <span
                className="font-medium"
                style={{
                  color: yearColors[year as unknown as keyof typeof yearColors],
                }}
              >
                ${(+allData[`cumulative_${year}`] / 1_000_000).toFixed(2)}M
              </span>
            </p>
          ) : null
        )}
      </div>
    )
  }
  return null
}

const CumulativeNetProfitChart = ({
  data,
}: {
  data: typeof cumulativeNetProfitData
}) => {
  // Generate full month/year mapping
  const chartData: any[] = MONTHS.map((month) => {
    const entry: any = { month }
    for (const item of data.data) {
      if (item.month === month) {
        const yearKey = `cumulative_${item.year}`
        entry[yearKey] = item.cumulativeNetProfit

        if (item.year === CURRENT_YEAR) {
          entry.netProfit = item.netProfit
          entry.revenue = item.revenue
          entry.expenses = item.expenses
        }
      }
    }
    return entry
  })

  return (
    <ResponsiveContainer width="99%" height={CHART_HEIGHT}>
      <ComposedChart data={chartData}>
        <CartesianGrid strokeDasharray="3 3" />
        <XAxis dataKey="month" tick={{ fontSize: 12 }} />

        {/* Left Y-axis for net profit bar */}
        <YAxis
          yAxisId="left"
          orientation="left"
          tick={{ fontSize: 12 }}
          tickFormatter={formatYAxis}
        />

        {/* Right Y-axis for cumulative lines */}
        <YAxis
          yAxisId="right"
          orientation="right"
          tick={{ fontSize: 12 }}
          tickFormatter={formatYAxis}
        />

        <Tooltip content={<CustomTooltip />} />
        <Legend
          verticalAlign="top"
          wrapperStyle={{ paddingBottom: 8, fontSize: 12 }}
          formatter={(value) => {
            if (value === "netProfit") return "Monthly Net Profit"
            if (value.startsWith("cumulative_"))
              return `${value.split("_")[1]} Cumulative`
            return value
          }}
        />

        {/* Net Profit Bar (Current Year only) */}
        <Bar
          yAxisId="left"
          dataKey="netProfit"
          name="netProfit"
          barSize={20}
          fill={yearColors[CURRENT_YEAR as keyof typeof yearColors]}
          opacity={0.8}
        />

        {/* Cumulative Lines (Separate Right Y-axis) */}
        {Object.keys(yearColors).map((year) => (
          <Line
            key={`line-${year}`}
            yAxisId="right"
            type="monotone"
            dataKey={`cumulative_${year}`}
            stroke={yearColors[year as unknown as keyof typeof yearColors]}
            strokeWidth={2}
            dot={{ r: 3 }}
            name={`Cumulative ${year}`}
          />
        ))}
      </ComposedChart>
    </ResponsiveContainer>
  )
}

export default CumulativeNetProfitChart
